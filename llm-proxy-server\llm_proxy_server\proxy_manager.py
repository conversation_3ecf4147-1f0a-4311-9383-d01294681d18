"""Complete proxy manager with all endpoints"""

import yaml
from pathlib import Path
from typing import Async<PERSON><PERSON><PERSON>, Dict, List
from ollama import AsyncClient as OllamaAsyncClient
import structlog
from fastapi import HTTPException

from .models import *
from .config import Settings

logger = structlog.get_logger()

class ProxyManager:
    def __init__(self, settings: Settings, load_balancer, metrics_manager):
        self.settings = settings
        self.load_balancer = load_balancer
        self.metrics_manager = metrics_manager
        self.clients: Dict[str, OllamaAsyncClient] = {}
        self.host_configs: List[HostConfig] = []
        self.model_registry: Dict[str, List[str]] = {}  # model -> [hosts]
        
    async def initialize(self):
        """Initialize proxy manager and discover hosts"""
        await self._load_host_configs()
        await self._initialize_clients()
        await self._discover_models()
        
    async def shutdown(self):
        """Cleanup resources"""
        for client in self.clients.values():
            if hasattr(client, 'close'):
                await client.close()
        
    async def _load_host_configs(self):
        """Load host configurations from YAML file"""
        config_path = Path(self.settings.hosts_config_path)
        if not config_path.exists():
            logger.warning("Hosts config file not found", path=str(config_path))
            return
            
        with open(config_path, 'r') as f:
            hosts_data = yaml.safe_load(f)
            
        for host_data in hosts_data:
            config = HostConfig(**host_data)
            self.host_configs.append(config)
            self.load_balancer.set_host_weight(config.host, config.weight)
            
        logger.info("Loaded host configurations", count=len(self.host_configs))
    
    async def _initialize_clients(self):
        """Initialize Ollama async clients for each host"""
        for config in self.host_configs:
            if config.enabled:
                try:
                    client = OllamaAsyncClient(host=config.host)
                    self.clients[config.host] = client
                    logger.info("Initialized client", host=config.host)
                except Exception as e:
                    logger.error("Failed to initialize client", host=config.host, error=str(e))
                    self.load_balancer.set_host_health(config.host, False)
    
    async def _discover_models(self):
        """Discover available models on each host"""
        for host, client in self.clients.items():
            try:
                models_response = await client.list()
                models = [model.model for model in models_response.models]
                
                for model in models:
                    if model not in self.model_registry:
                        self.model_registry[model] = []
                    if host not in self.model_registry[model]:
                        self.model_registry[model].append(host)
                        
                logger.info("Discovered models", host=host, models=models)
                self.load_balancer.set_host_health(host, True)
                
            except Exception as e:
                logger.error("Failed to discover models", host=host, error=str(e))
                self.load_balancer.set_host_health(host, False)

    async def handle_chat_stream(self, request: ChatRequest, user: dict) -> AsyncIterator[ChatResponse]:
        """Handle streaming chat requests"""
        client = await self._get_client_for_model(request.model)
        host = self._get_host_for_client(client)
        
        self.load_balancer.increment_connections(host)
        self.metrics_manager.increment_active_connections(host)
        
        try:
            # Convert messages to proper format
            messages = [msg.dict() for msg in request.messages]

            async for chunk in client.chat(
                model=request.model,
                messages=messages,
                stream=True,
                format=request.format,
                options=request.options,
                keep_alive=request.keep_alive
            ):
                response_chunk = ChatResponse(
                    model=chunk.model or request.model,
                    created_at=chunk.created_at or "",
                    message=ChatMessage(
                        role=chunk.message.role if chunk.message else "assistant",
                        content=chunk.message.content if chunk.message else ""
                    ),
                    done=chunk.done or False,
                    total_duration=chunk.total_duration,
                    load_duration=chunk.load_duration,
                    prompt_eval_count=chunk.prompt_eval_count,
                    prompt_eval_duration=chunk.prompt_eval_duration,
                    eval_count=chunk.eval_count,
                    eval_duration=chunk.eval_duration
                )
                yield response_chunk
                
        except Exception as e:
            self.metrics_manager.record_request("chat_stream", "POST", 500, 0, host=host, model=request.model, error=type(e).__name__)
            logger.error("Streaming chat failed", error=str(e), model=request.model, host=host)
            raise
        finally:
            self.load_balancer.decrement_connections(host)
            self.metrics_manager.decrement_active_connections(host)

    async def handle_chat(self, request: ChatRequest, user: dict) -> ChatResponse:
        """Handle non-streaming chat requests"""
        client = await self._get_client_for_model(request.model)
        host = self._get_host_for_client(client)
        
        self.load_balancer.increment_connections(host)
        self.metrics_manager.increment_active_connections(host)
        
        try:
            # Convert messages to proper format
            messages = [msg.dict() for msg in request.messages]

            response = await client.chat(
                model=request.model,
                messages=messages,
                stream=False,
                format=request.format,
                options=request.options,
                keep_alive=request.keep_alive
            )
            
            return ChatResponse(
                model=response.model or request.model,
                created_at=response.created_at or "",
                message=ChatMessage(
                    role=response.message.role if response.message else "assistant",
                    content=response.message.content if response.message else ""
                ),
                done=response.done or True,
                total_duration=response.total_duration,
                load_duration=response.load_duration,
                prompt_eval_count=response.prompt_eval_count,
                prompt_eval_duration=response.prompt_eval_duration,
                eval_count=response.eval_count,
                eval_duration=response.eval_duration
            )
            
        except Exception as e:
            self.metrics_manager.record_request("chat", "POST", 500, 0, host=host, model=request.model, error=type(e).__name__)
            logger.error("Chat failed", error=str(e), model=request.model, host=host)
            raise
        finally:
            self.load_balancer.decrement_connections(host)
            self.metrics_manager.decrement_active_connections(host)

    async def handle_generate_stream(self, request: GenerateRequest, user: dict) -> AsyncIterator[GenerateResponse]:
        """Handle streaming generate requests"""
        client = await self._get_client_for_model(request.model)
        host = self._get_host_for_client(client)
        
        self.load_balancer.increment_connections(host)
        self.metrics_manager.increment_active_connections(host)
        
        try:
            ollama_request = {
                "model": request.model,
                "prompt": request.prompt,
                "stream": True,
            }
            
            if request.system:
                ollama_request["system"] = request.system
            if request.template:
                ollama_request["template"] = request.template
            if request.context:
                ollama_request["context"] = request.context
            if request.options:
                ollama_request.update(request.options)
            if request.format:
                ollama_request["format"] = request.format
            if request.keep_alive:
                ollama_request["keep_alive"] = request.keep_alive
            if request.raw:
                ollama_request["raw"] = request.raw
                
            # Call generate with explicit parameters to match the method signature
            async for chunk in client.generate(
                model=ollama_request["model"],
                prompt=ollama_request["prompt"],
                stream=True,
                system=ollama_request.get("system"),
                template=ollama_request.get("template"),
                context=ollama_request.get("context"),
                format=ollama_request.get("format"),
                keep_alive=ollama_request.get("keep_alive"),
                raw=ollama_request.get("raw"),
                options=ollama_request.get("options")
            ):
                response_chunk = GenerateResponse(
                    model=chunk.get("model", request.model),
                    created_at=chunk.get("created_at", ""),
                    response=chunk.get("response", ""),
                    done=chunk.get("done", False),
                    context=chunk.get("context"),
                    total_duration=chunk.get("total_duration"),
                    load_duration=chunk.get("load_duration"),
                    prompt_eval_count=chunk.get("prompt_eval_count"),
                    prompt_eval_duration=chunk.get("prompt_eval_duration"),
                    eval_count=chunk.get("eval_count"),
                    eval_duration=chunk.get("eval_duration")
                )
                yield response_chunk
                
        except Exception as e:
            self.metrics_manager.record_request("generate_stream", "POST", 500, 0, host=host, model=request.model, error=type(e).__name__)
            logger.error("Streaming generate failed", error=str(e), model=request.model, host=host)
            raise
        finally:
            self.load_balancer.decrement_connections(host)
            self.metrics_manager.decrement_active_connections(host)

    async def handle_generate(self, request: GenerateRequest, user: dict) -> GenerateResponse:
        """Handle non-streaming generate requests"""
        client = await self._get_client_for_model(request.model)
        host = self._get_host_for_client(client)
        
        self.load_balancer.increment_connections(host)
        self.metrics_manager.increment_active_connections(host)
        
        try:
            ollama_request = {
                "model": request.model,
                "prompt": request.prompt,
                "stream": False,
            }
            
            if request.system:
                ollama_request["system"] = request.system
            if request.template:
                ollama_request["template"] = request.template
            if request.context:
                ollama_request["context"] = request.context
            if request.options:
                ollama_request.update(request.options)
            if request.format:
                ollama_request["format"] = request.format
            if request.keep_alive:
                ollama_request["keep_alive"] = request.keep_alive
            if request.raw:
                ollama_request["raw"] = request.raw
                
            # Build generate kwargs, only including non-None values
            generate_kwargs = {
                "model": ollama_request["model"],
                "prompt": ollama_request["prompt"],
                "stream": False,  # Explicitly set to False for non-streaming
            }

            # Only add optional parameters if they have values
            if ollama_request.get("system"):
                generate_kwargs["system"] = ollama_request["system"]
            if ollama_request.get("template"):
                generate_kwargs["template"] = ollama_request["template"]
            if ollama_request.get("context"):
                generate_kwargs["context"] = ollama_request["context"]
            if ollama_request.get("format"):
                generate_kwargs["format"] = ollama_request["format"]
            if ollama_request.get("keep_alive"):
                generate_kwargs["keep_alive"] = ollama_request["keep_alive"]
            if ollama_request.get("raw"):
                generate_kwargs["raw"] = ollama_request["raw"]
            if ollama_request.get("options"):
                generate_kwargs["options"] = ollama_request["options"]

            response = await client.generate(**generate_kwargs)
            
            return GenerateResponse(
                model=response.get("model", request.model),
                created_at=response.get("created_at", ""),
                response=response.get("response", ""),
                done=response.get("done", True),
                context=response.get("context"),
                total_duration=response.get("total_duration"),
                load_duration=response.get("load_duration"),
                prompt_eval_count=response.get("prompt_eval_count"),
                prompt_eval_duration=response.get("prompt_eval_duration"),
                eval_count=response.get("eval_count"),
                eval_duration=response.get("eval_duration")
            )
            
        except Exception as e:
            self.metrics_manager.record_request("generate", "POST", 500, 0, host=host, model=request.model, error=type(e).__name__)
            logger.error("Generate failed", error=str(e), model=request.model, host=host)
            raise
        finally:
            self.load_balancer.decrement_connections(host)
            self.metrics_manager.decrement_active_connections(host)

    async def handle_embed(self, request: EmbedRequest, user: dict) -> EmbedResponse:
        """Handle embed requests"""
        client = await self._get_client_for_model(request.model)
        host = self._get_host_for_client(client)
        
        try:
            ollama_request = {
                "model": request.model,
                "prompt": request.prompt,
            }
            
            if request.options:
                ollama_request.update(request.options)
            if request.keep_alive:
                ollama_request["keep_alive"] = request.keep_alive
                
            response = await client.embed(**ollama_request)
            
            return EmbedResponse(embedding=response.embeddings[0])
            
        except Exception as e:
            self.metrics_manager.record_request("embed", "POST", 500, 0, host=host, model=request.model, error=type(e).__name__)
            logger.error("Embed failed", error=str(e), model=request.model, host=host)
            raise

    async def handle_show(self, request: ShowRequest, user: dict) -> ShowResponse:
        """Handle show requests"""
        client = await self._get_client_for_model(request.model)
        host = self._get_host_for_client(client)
        
        try:
            response = await client.show(request.model)
            
            return ShowResponse(
                modelfile=response.get("modelfile", ""),
                parameters=response.get("parameters", ""),
                template=response.get("template", ""),
                details=response.get("details", {})
            )
            
        except Exception as e:
            self.metrics_manager.record_request("show", "POST", 500, 0, host=host, model=request.model, error=type(e).__name__)
            logger.error("Show failed", error=str(e), model=request.model, host=host)
            raise

    async def _get_client_for_model(self, model: str) -> OllamaAsyncClient:
        """Get the best client for a specific model using load balancer"""
        if model not in self.model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model} not found")
        
        available_hosts = self.model_registry[model]
        selected_host = await self.load_balancer.select_host(model, available_hosts)
        
        if selected_host not in self.clients:
            raise HTTPException(status_code=503, detail=f"Host {selected_host} not available")
            
        return self.clients[selected_host]
    
    def _get_host_for_client(self, client: OllamaAsyncClient) -> str:
        """Get host name for a client"""
        for host, c in self.clients.items():
            if c == client:
                return host
        return "unknown"

    async def list_models(self) -> List[ModelInfo]:
        """List all available models"""
        models = []
        seen_models = set()
        
        for host, client in self.clients.items():
            try:
                response = await client.list()
                for model in response.models:
                    if model.model and model.model not in seen_models:
                        models.append(ModelInfo(
                            name=model.model,
                            model=model.model,
                            modified_at=model.modified_at.isoformat() if model.modified_at else "",
                            size=model.size or 0,
                            digest=model.digest or "",
                            details=model.details.dict() if model.details else {}
                        ))
                        seen_models.add(model.model)
            except Exception as e:
                logger.error("Failed to list models", host=host, error=str(e))
        
        return models

    async def get_status(self) -> ProxyStatus:
        """Get proxy server status"""
        total_hosts = len(self.host_configs)
        active_hosts = len([h for h in self.clients.keys() if self.load_balancer.host_health.get(h, True)])
        
        metrics = self.metrics_manager.generate_metrics()
        
        return ProxyStatus(
            total_hosts=total_hosts,
            active_hosts=active_hosts,
            total_requests=metrics["total_requests"],
            failed_requests=metrics["total_errors"],
            average_response_time=sum(metrics["average_response_times"].values()) / len(metrics["average_response_times"]) if metrics["average_response_times"] else 0,
            hosts=[
                {
                    "host": config.host,
                    "enabled": config.enabled,
                    "healthy": self.load_balancer.host_health.get(config.host, True),
                    "active_connections": self.metrics_manager.active_connections.get(config.host, 0)
                }
                for config in self.host_configs
            ]
        )